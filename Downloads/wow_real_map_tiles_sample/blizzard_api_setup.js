// Blizzard API Setup for WoW Maps
// You need to register at https://develop.battle.net/ to get credentials

class BlizzardWoWAPI {
  constructor(clientId, clientSecret, region = 'us') {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.region = region;
    this.accessToken = null;
    this.baseUrl = `https://${region}.api.blizzard.com`;
  }

  // Get OAuth access token
  async getAccessToken() {
    const response = await fetch(`https://${this.region}.battle.net/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${btoa(this.clientId + ':' + this.clientSecret)}`
      },
      body: 'grant_type=client_credentials'
    });
    
    const data = await response.json();
    this.accessToken = data.access_token;
    return this.accessToken;
  }

  // Get all available maps
  async getMaps() {
    if (!this.accessToken) await this.getAccessToken();
    
    const response = await fetch(`${this.baseUrl}/data/wow/map/index?namespace=static-${this.region}&locale=en_US&access_token=${this.accessToken}`);
    return await response.json();
  }

  // Get specific map details
  async getMap(mapId) {
    if (!this.accessToken) await this.getAccessToken();
    
    const response = await fetch(`${this.baseUrl}/data/wow/map/${mapId}?namespace=static-${this.region}&locale=en_US&access_token=${this.accessToken}`);
    return await response.json();
  }

  // Get map media (actual image URLs)
  async getMapMedia(mapId) {
    if (!this.accessToken) await this.getAccessToken();
    
    const response = await fetch(`${this.baseUrl}/data/wow/media/map/${mapId}?namespace=static-${this.region}&locale=en_US&access_token=${this.accessToken}`);
    return await response.json();
  }

  // Get realm list
  async getRealms() {
    if (!this.accessToken) await this.getAccessToken();
    
    const response = await fetch(`${this.baseUrl}/data/wow/realm/index?namespace=dynamic-${this.region}&locale=en_US&access_token=${this.accessToken}`);
    return await response.json();
  }
}

// Usage example:
async function setupBlizzardMaps() {
  // You need to get these from https://develop.battle.net/
  const CLIENT_ID = 'your_client_id_here';
  const CLIENT_SECRET = 'your_client_secret_here';
  
  const api = new BlizzardWoWAPI(CLIENT_ID, CLIENT_SECRET);
  
  try {
    // Get all maps
    const maps = await api.getMaps();
    console.log('Available maps:', maps);
    
    // Get specific map (example: Stormwind City)
    const stormwindMap = await api.getMap(1519); // Stormwind City map ID
    console.log('Stormwind map:', stormwindMap);
    
    // Get map images
    const stormwindMedia = await api.getMapMedia(1519);
    console.log('Stormwind images:', stormwindMedia);
    
    return { maps, stormwindMap, stormwindMedia };
  } catch (error) {
    console.error('Error fetching Blizzard API data:', error);
  }
}

// Popular WoW Map IDs (Classic/Retail)
const POPULAR_MAP_IDS = {
  // World Maps
  EASTERN_KINGDOMS: 13,
  KALIMDOR: 12,
  OUTLAND: 466,
  NORTHREND: 485,
  
  // Major Cities
  STORMWIND: 1519,
  ORGRIMMAR: 1637,
  IRONFORGE: 1537,
  THUNDER_BLUFF: 1638,
  UNDERCITY: 1497,
  DARNASSUS: 1657,
  
  // Popular Zones
  ELWYNN_FOREST: 1429,
  DUROTAR: 1411,
  WESTFALL: 1436,
  BARRENS: 1413,
  REDRIDGE: 1433,
  STONETALON: 1442,
  DARKSHORE: 1439,
  LOCH_MODAN: 1432
};

// Alternative: Use WoWHead's tile service (no API key needed)
function createWoWHeadTileLayer(zoneName, map) {
  return L.tileLayer(`https://wow.zamimg.com/images/wow/maps/enus/zoom/${zoneName}/{z}/{x}/{y}.jpg`, {
    attribution: 'Map data © Blizzard Entertainment, Images © WoWHead',
    maxZoom: 6,
    tileSize: 256,
    errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  });
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { BlizzardWoWAPI, POPULAR_MAP_IDS, createWoWHeadTileLayer };
}
