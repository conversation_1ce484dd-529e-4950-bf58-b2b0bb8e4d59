#!/usr/bin/env python3
"""
WoW Map Downloader
Downloads high-quality WoW maps from various sources
"""

import os
import requests
import time
from urllib.parse import urljoin

class WoWMapDownloader:
    def __init__(self, output_dir="downloaded_maps"):
        self.output_dir = output_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
    
    def download_wowhead_tiles(self, zone_name, max_zoom=6, max_x=10, max_y=10):
        """Download tiles from WoWHead"""
        print(f"Downloading {zone_name} tiles from WoWHead...")
        
        zone_dir = os.path.join(self.output_dir, f"wowhead_{zone_name}")
        os.makedirs(zone_dir, exist_ok=True)
        
        base_url = f"https://wow.zamimg.com/images/wow/maps/enus/zoom/{zone_name}/"
        
        for z in range(max_zoom + 1):
            zoom_dir = os.path.join(zone_dir, str(z))
            os.makedirs(zoom_dir, exist_ok=True)
            
            for x in range(max_x):
                x_dir = os.path.join(zoom_dir, str(x))
                os.makedirs(x_dir, exist_ok=True)
                
                for y in range(max_y):
                    tile_url = f"{base_url}{z}-{x}-{y}.jpg"
                    tile_path = os.path.join(x_dir, f"{y}.jpg")
                    
                    if not os.path.exists(tile_path):
                        try:
                            response = self.session.get(tile_url, timeout=10)
                            if response.status_code == 200:
                                with open(tile_path, 'wb') as f:
                                    f.write(response.content)
                                print(f"Downloaded: {z}/{x}/{y}")
                            else:
                                print(f"Failed: {z}/{x}/{y} (HTTP {response.status_code})")
                        except Exception as e:
                            print(f"Error downloading {z}/{x}/{y}: {e}")
                        
                        # Be nice to the server
                        time.sleep(0.1)
    
    def download_high_res_map(self, zone_name, url_template=None):
        """Download a single high-resolution map"""
        if url_template is None:
            # Try different common patterns
            patterns = [
                f"https://wow.zamimg.com/images/wow/maps/enus/original/{zone_name}.jpg",
                f"https://wow.zamimg.com/images/wow/maps/enus/large/{zone_name}.jpg",
                f"https://wowimg.zamimg.com/images/wow/maps/{zone_name}.jpg"
            ]
        else:
            patterns = [url_template.format(zone=zone_name)]
        
        for url in patterns:
            try:
                print(f"Trying: {url}")
                response = self.session.get(url, timeout=30)
                if response.status_code == 200:
                    filename = f"{zone_name}_highres.jpg"
                    filepath = os.path.join(self.output_dir, filename)
                    
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    print(f"Successfully downloaded: {filename}")
                    return filepath
                else:
                    print(f"Failed: HTTP {response.status_code}")
            except Exception as e:
                print(f"Error: {e}")
        
        print(f"Could not download high-res map for {zone_name}")
        return None

# Popular WoW zones to download
POPULAR_ZONES = [
    'azeroth',      # World map
    'stormwind',    # Stormwind City
    'orgrimmar',    # Orgrimmar
    'ironforge',    # Ironforge
    'elwynn',       # Elwynn Forest
    'durotar',      # Durotar
    'westfall',     # Westfall
    'barrens',      # The Barrens
    'redridge',     # Redridge Mountains
    'darkshore',    # Darkshore
    'loch_modan',   # Loch Modan
    'stonetalon'    # Stonetalon Mountains
]

def main():
    downloader = WoWMapDownloader()
    
    print("WoW Map Downloader")
    print("==================")
    print("1. Download WoWHead tiles")
    print("2. Download high-res maps")
    print("3. Download specific zone")
    
    choice = input("Choose option (1-3): ").strip()
    
    if choice == "1":
        print("\nAvailable zones:", ", ".join(POPULAR_ZONES))
        zone = input("Enter zone name: ").strip()
        if zone:
            downloader.download_wowhead_tiles(zone, max_zoom=4, max_x=8, max_y=8)
    
    elif choice == "2":
        print("Downloading high-res maps for popular zones...")
        for zone in POPULAR_ZONES:
            downloader.download_high_res_map(zone)
            time.sleep(1)  # Be nice to the server
    
    elif choice == "3":
        zone = input("Enter zone name: ").strip()
        if zone:
            print(f"Downloading {zone}...")
            downloader.download_high_res_map(zone)
            downloader.download_wowhead_tiles(zone, max_zoom=3, max_x=6, max_y=6)
    
    print("\nDownload complete!")

if __name__ == "__main__":
    main()
