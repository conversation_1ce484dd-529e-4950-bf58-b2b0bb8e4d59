WoW Classic — 真实地图切片 + 样本 JSON 指南
--------------------------------------------------
说明：
本包没有包含完整的 Teeb 13000x12000 原始地图（版权/带宽与运行环境限制），
但包含了：
  1) 下载与切片的自动化脚本示例（从 Internet Archive 下载原图并使用 gdal2tiles 切片）
  2) 一个小的示例 tiles/ 目录（zoom 0..2）与前端 demo，方便你先行预览 UI。
  3) 示例数据 map_data.json（包含任务 / 掉落 / 宠物 的样例条目）
  4) index.html：Leaflet 前端 demo，加载本地 tiles 与 map_data.json

步骤（推荐在本地机器上执行，以获得真实高分地图与完整瓦片）：
A. 下载原始高分图（Teeb 的 13k x 12k PNG）
   1) 在浏览器打开： https://archive.org/details/wow_classic_high_resolution_world_terrain_map_azeroth
   2) 点击 'PNG download' 获取文件名类似于: wow_classic_high_resolution_world_terrain_map_azeroth.png
   或在命令行使用 wget（示例）：
      wget -O azeroth_full.png "https://archive.org/download/wow_classic_high_resolution_world_terrain_map_azeroth/wow_classic_high_resolution_world_terrain_map_azeroth.png"

B. 切片（需要安装 gdal）
   - 安装 GDAL:
       Ubuntu: sudo apt-get install gdal-bin
       macOS (Homebrew): brew install gdal
   - 使用 gdal2tiles 切片（示例, 输出到 tiles/ 目录）:
       gdal2tiles.py -z 0-5 -w none -r lanczos azeroth_full.png tiles/
     说明：-z 级别根据你想要的缩放范围调整；tilesize 默认是 256，如果要 512 参考 gdal2tiles 文档。

C. 将 tiles/ 放到 demo 包的同级目录，运行 demo:
   - 确保 index.html 与 tiles/ 在同一目录下。
   - 在该目录启动本地服务器：
       python -m http.server 8000
   - 打开浏览器访问 http://localhost:8000/index.html

版权与来源声明：
  - 地图素材来源：Teeb 的高分地图，托管于 Internet Archive:
    https://archive.org/details/wow_classic_high_resolution_world_terrain_map_azeroth
  - 数据来源建议：Wowhead (classic.wowhead.com)、ClassicDB、Wowpedia（用于抓取任务 / 掉落 / 宠物 数据），请遵循各站点的使用条款与 robots.txt。
