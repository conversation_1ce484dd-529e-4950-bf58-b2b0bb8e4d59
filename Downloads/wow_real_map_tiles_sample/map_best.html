<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8"/>
<title>WoW 地图 - 最佳版本</title>
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
<style>
  html, body, #map { height:100%; margin:0; padding:0; }
  .controls {
    position:absolute; top:10px; left:10px;
    z-index:1000; background:#fffbea; padding:8px;
    border-radius:6px; border:1px solid #ccc;
  }
</style>
</head>
<body>
<div class="controls">
  <strong>图层</strong><br/>
  <label><input type="checkbox" id="toggleQuests" checked/> 任务</label><br/>
  <label><input type="checkbox" id="toggleLoot" checked/> 掉落</label><br/>
  <label><input type="checkbox" id="togglePets" checked/> 宠物</label>
</div>
<div id="map"></div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
// Create a simple map using only image overlays - no tile layers
var map = L.map('map', {
  crs: L.CRS.Simple,
  minZoom: 0,
  maxZoom: 3
});

// Define image bounds for different zoom levels
var imageBounds = {
  0: [[0, 0], [256, 256]],      // Base tile: 256x256
  1: [[0, 0], [512, 512]],      // 4 tiles combined: 512x512
  2: [[0, 0], [1024, 768]],     // Higher resolution: 1024x768
  3: [[0, 0], [2048, 1536]]     // Highest resolution: 2048x1536
};

// Current zoom level and image overlay
var currentZoom = 0;
var currentOverlay = null;

// Function to update image overlay based on zoom level
function updateImageOverlay() {
  var zoom = Math.round(map.getZoom());
  
  if (zoom !== currentZoom) {
    // Remove current overlay
    if (currentOverlay) {
      map.removeLayer(currentOverlay);
    }
    
    // Add new overlay based on zoom level
    var imageUrl, bounds;
    
    switch(zoom) {
      case 0:
        imageUrl = 'tiles/0/0/0.png';
        bounds = imageBounds[0];
        break;
      case 1:
        // Use a higher resolution tile if available, otherwise scale up base tile
        imageUrl = 'tiles/0/0/0.png';  // We'll scale this up
        bounds = imageBounds[1];
        break;
      case 2:
        imageUrl = 'tiles/0/0/0.png';  // Scale up further
        bounds = imageBounds[2];
        break;
      case 3:
        imageUrl = 'tiles/0/0/0.png';  // Maximum scale
        bounds = imageBounds[3];
        break;
      default:
        imageUrl = 'tiles/0/0/0.png';
        bounds = imageBounds[0];
    }
    
    currentOverlay = L.imageOverlay(imageUrl, bounds);
    map.addLayer(currentOverlay);
    currentZoom = zoom;
  }
}

// Set initial view and overlay
map.setView([128, 128], 0);
updateImageOverlay();

// Update overlay on zoom
map.on('zoomend', updateImageOverlay);

// Create layer groups for different marker types
var questsLayer = L.layerGroup().addTo(map);
var lootLayer   = L.layerGroup().addTo(map);
var petsLayer   = L.layerGroup().addTo(map);

// Load and display markers with better coordinate transformation
fetch('map_data_sample.json').then(r=>r.json()).then(data=>{
  data.quests.forEach(q=>{
    // Better coordinate transformation based on the actual coordinate ranges
    // Assuming coordinates are in a game coordinate system that needs mapping to image pixels
    var x = (q.coords[0] / 13312) * 256;  // Scale X to image width
    var y = 256 - (q.coords[1] / 9984) * 256;  // Scale Y and flip (game Y is often inverted)
    
    var marker = L.circleMarker([y, x], {radius:6, color:'#ff0000', fillColor:'#ff4444', fillOpacity:0.8, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#d2691e;">🎯 任务: ${q.name}</h3>
         <p><strong>等级:</strong> ${q.level}</p>
         <p><strong>区域:</strong> ${q.zone}</p>
         <p><strong>任务给予者:</strong> ${q.giver.name}</p>
         <p><strong>备注:</strong> ${q.notes}</p>
         <p><a href="${q.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>原始坐标: (${q.coords[0]}, ${q.coords[1]})</small></p>
         <p><small>地图坐标: (${Math.round(x)}, ${Math.round(y)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(questsLayer);
  });
  
  data.loot.forEach(l=>{
    var x = (l.coords[0] / 13312) * 256;
    var y = 256 - (l.coords[1] / 9984) * 256;
    
    var marker = L.marker([y, x], {
      icon: L.divIcon({
        className: 'loot-marker',
        html: '💰',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
      })
    })
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#8b4513;">💰 掉落: ${l.name}</h3>
         <p><strong>来源:</strong> ${l.source}</p>
         <p><strong>区域:</strong> ${l.zone}</p>
         <p><strong>掉落率:</strong> ${l.drop_rate}</p>
         <p><strong>物品ID:</strong> ${l.item_id}</p>
         <p><a href="${l.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>原始坐标: (${l.coords[0]}, ${l.coords[1]})</small></p>
         <p><small>地图坐标: (${Math.round(x)}, ${Math.round(y)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(lootLayer);
  });
  
  data.pets.forEach(p=>{
    var x = (p.coords[0] / 13312) * 256;
    var y = 256 - (p.coords[1] / 9984) * 256;
    
    var marker = L.circleMarker([y, x], {radius:8, color:'#0066ff', fillColor:'#4488ff', fillOpacity:0.6, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#4169e1;">🐾 宠物: ${p.name}</h3>
         <p><strong>获取方式:</strong> ${p.obtain}</p>
         <p><strong>区域:</strong> ${p.zone}</p>
         <p><strong>备注:</strong> ${p.notes}</p>
         <p><strong>宠物ID:</strong> ${p.id}</p>
         <p><a href="${p.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>原始坐标: (${p.coords[0]}, ${p.coords[1]})</small></p>
         <p><small>地图坐标: (${Math.round(x)}, ${Math.round(y)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(petsLayer);
  });
});

// Layer toggle controls
document.getElementById('toggleQuests').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(questsLayer); else map.removeLayer(questsLayer);
});
document.getElementById('toggleLoot').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(lootLayer); else map.removeLayer(lootLayer);
});
document.getElementById('togglePets').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(petsLayer); else map.removeLayer(petsLayer);
});

// Add CSS for custom markers
var style = document.createElement('style');
style.textContent = `
  .loot-marker {
    background: none !important;
    border: none !important;
    font-size: 16px;
    text-align: center;
    line-height: 20px;
  }
`;
document.head.appendChild(style);
</script>
</body>
</html>
