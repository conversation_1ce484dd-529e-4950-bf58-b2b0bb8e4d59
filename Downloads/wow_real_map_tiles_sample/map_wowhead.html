<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8"/>
<title>WoW 地图 - WoWHead 高质量版本</title>
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
<style>
  html, body, #map { height:100%; margin:0; padding:0; }
  .controls {
    position:absolute; top:10px; left:10px;
    z-index:1000; background:#fffbea; padding:8px;
    border-radius:6px; border:1px solid #ccc;
  }
  .map-selector {
    position:absolute; top:10px; right:10px;
    z-index:1000; background:#fffbea; padding:8px;
    border-radius:6px; border:1px solid #ccc;
  }
</style>
</head>
<body>
<div class="controls">
  <strong>图层</strong><br/>
  <label><input type="checkbox" id="toggleQuests" checked/> 任务</label><br/>
  <label><input type="checkbox" id="toggleLoot" checked/> 掉落</label><br/>
  <label><input type="checkbox" id="togglePets" checked/> 宠物</label>
</div>

<div class="map-selector">
  <strong>地图选择</strong><br/>
  <select id="mapSelect">
    <option value="azeroth">艾泽拉斯 (世界地图)</option>
    <option value="stormwind">暴风城</option>
    <option value="orgrimmar">奥格瑞玛</option>
    <option value="ironforge">铁炉堡</option>
    <option value="elwynn">艾尔文森林</option>
    <option value="durotar">杜隆塔尔</option>
    <option value="westfall">西部荒野</option>
    <option value="barrens">贫瘠之地</option>
  </select>
</div>

<div id="map"></div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
// Create map with proper coordinate system
var map = L.map('map', {
  crs: L.CRS.EPSG3857, // Use standard web mercator
  center: [0, 0],
  zoom: 2,
  minZoom: 0,
  maxZoom: 6
});

// Current tile layer
var currentTileLayer = null;

// Map configurations for different zones
var mapConfigs = {
  azeroth: {
    name: '艾泽拉斯',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/azeroth/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [0, 0],
    zoom: 2
  },
  stormwind: {
    name: '暴风城',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/stormwind/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [20, -10],
    zoom: 4
  },
  orgrimmar: {
    name: '奥格瑞玛',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/orgrimmar/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [10, 20],
    zoom: 4
  },
  ironforge: {
    name: '铁炉堡',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/ironforge/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [30, -20],
    zoom: 4
  },
  elwynn: {
    name: '艾尔文森林',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/elwynn/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [15, -15],
    zoom: 3
  },
  durotar: {
    name: '杜隆塔尔',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/durotar/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [5, 15],
    zoom: 3
  },
  westfall: {
    name: '西部荒野',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/westfall/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [10, -25],
    zoom: 3
  },
  barrens: {
    name: '贫瘠之地',
    url: 'https://wow.zamimg.com/images/wow/maps/enus/zoom/barrens/{z}/{x}/{y}.jpg',
    bounds: [[-85, -180], [85, 180]],
    center: [-5, 25],
    zoom: 3
  }
};

// Function to switch maps
function switchMap(mapKey) {
  var config = mapConfigs[mapKey];
  
  // Remove current tile layer
  if (currentTileLayer) {
    map.removeLayer(currentTileLayer);
  }
  
  // Add new tile layer
  currentTileLayer = L.tileLayer(config.url, {
    attribution: 'Map data © Blizzard Entertainment, Images © WoWHead',
    maxZoom: 6,
    tileSize: 256,
    errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  }).addTo(map);
  
  // Set view to map center
  map.setView(config.center, config.zoom);
}

// Initialize with Azeroth map
switchMap('azeroth');

// Map selector event
document.getElementById('mapSelect').addEventListener('change', function(e) {
  switchMap(e.target.value);
});

// Create layer groups for different marker types
var questsLayer = L.layerGroup().addTo(map);
var lootLayer   = L.layerGroup().addTo(map);
var petsLayer   = L.layerGroup().addTo(map);

// Function to convert game coordinates to lat/lng
function gameToLatLng(x, y, mapKey = 'azeroth') {
  // This is a simplified conversion - you'll need to adjust based on your coordinate system
  var lat = (y / 9984) * 170 - 85;  // Scale to -85 to 85 (latitude range)
  var lng = (x / 13312) * 360 - 180; // Scale to -180 to 180 (longitude range)
  return [lat, lng];
}

// Load and display markers
fetch('map_data_sample.json').then(r=>r.json()).then(data=>{
  data.quests.forEach(q=>{
    var latLng = gameToLatLng(q.coords[0], q.coords[1]);
    
    var marker = L.circleMarker(latLng, {radius:8, color:'#ff0000', fillColor:'#ff4444', fillOpacity:0.8, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#d2691e;">🎯 任务: ${q.name}</h3>
         <p><strong>等级:</strong> ${q.level}</p>
         <p><strong>区域:</strong> ${q.zone}</p>
         <p><strong>任务给予者:</strong> ${q.giver.name}</p>
         <p><strong>备注:</strong> ${q.notes}</p>
         <p><a href="${q.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>游戏坐标: (${q.coords[0]}, ${q.coords[1]})</small></p>
         <p><small>地图坐标: (${latLng[0].toFixed(2)}, ${latLng[1].toFixed(2)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView(latLng, Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(questsLayer);
  });
  
  data.loot.forEach(l=>{
    var latLng = gameToLatLng(l.coords[0], l.coords[1]);
    
    var marker = L.marker(latLng, {
      icon: L.divIcon({
        className: 'loot-marker',
        html: '💰',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
      })
    })
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#8b4513;">💰 掉落: ${l.name}</h3>
         <p><strong>来源:</strong> ${l.source}</p>
         <p><strong>区域:</strong> ${l.zone}</p>
         <p><strong>掉落率:</strong> ${l.drop_rate}</p>
         <p><strong>物品ID:</strong> ${l.item_id}</p>
         <p><a href="${l.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>游戏坐标: (${l.coords[0]}, ${l.coords[1]})</small></p>
         <p><small>地图坐标: (${latLng[0].toFixed(2)}, ${latLng[1].toFixed(2)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView(latLng, Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(lootLayer);
  });
  
  data.pets.forEach(p=>{
    var latLng = gameToLatLng(p.coords[0], p.coords[1]);
    
    var marker = L.circleMarker(latLng, {radius:10, color:'#0066ff', fillColor:'#4488ff', fillOpacity:0.6, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#4169e1;">🐾 宠物: ${p.name}</h3>
         <p><strong>获取方式:</strong> ${p.obtain}</p>
         <p><strong>区域:</strong> ${p.zone}</p>
         <p><strong>备注:</strong> ${p.notes}</p>
         <p><strong>宠物ID:</strong> ${p.id}</p>
         <p><a href="${p.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>游戏坐标: (${p.coords[0]}, ${p.coords[1]})</small></p>
         <p><small>地图坐标: (${latLng[0].toFixed(2)}, ${latLng[1].toFixed(2)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView(latLng, Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(petsLayer);
  });
});

// Layer toggle controls
document.getElementById('toggleQuests').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(questsLayer); else map.removeLayer(questsLayer);
});
document.getElementById('toggleLoot').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(lootLayer); else map.removeLayer(lootLayer);
});
document.getElementById('togglePets').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(petsLayer); else map.removeLayer(petsLayer);
});

// Add CSS for custom markers
var style = document.createElement('style');
style.textContent = `
  .loot-marker {
    background: none !important;
    border: none !important;
    font-size: 16px;
    text-align: center;
    line-height: 20px;
  }
`;
document.head.appendChild(style);
</script>
</body>
</html>
