<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8"/>
<title>WoW 真实地图切片 Demo</title>
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
<style>
  html, body, #map { height:100%; margin:0; padding:0; }
  .controls {
    position:absolute; top:10px; left:10px;
    z-index:1000; background:#fffbea; padding:8px;
    border-radius:6px; border:1px solid #ccc;
  }
</style>
</head>
<body>
<div class="controls">
  <strong>图层</strong><br/>
  <label><input type="checkbox" id="toggleQuests" checked/> 任务</label><br/>
  <label><input type="checkbox" id="toggleLoot" checked/> 掉落</label><br/>
  <label><input type="checkbox" id="togglePets" checked/> 宠物</label>
</div>
<div id="map"></div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
// Use standard web mercator projection instead of Simple CRS
var map = L.map('map', {
  minZoom: 0,
  maxZoom: 5,
  center: [0, 0],
  zoom: 1
});

// Custom tile layer that transforms coordinates to match our tile structure
L.tileLayer('tiles/{z}/{x}/{y}.png', {
  tileSize: 256,
  noWrap: true,
  attribution: 'WoW Map Data'
}).addTo(map);

// ---- 以下是加载任务/掉落/宠物数据 ----
var questsLayer = L.layerGroup().addTo(map);
var lootLayer   = L.layerGroup().addTo(map);
var petsLayer   = L.layerGroup().addTo(map);

fetch('map_data_sample.json').then(r=>r.json()).then(data=>{
  data.quests.forEach(q=>{
    // Convert pixel coordinates to lat/lng (simple scaling)
    var lat = (q.coords[1] / 9984) * 180 - 90;  // Scale Y to -90 to 90
    var lng = (q.coords[0] / 13312) * 360 - 180; // Scale X to -180 to 180
    L.circleMarker([lat, lng], {radius:6, color:'red'})
     .bindPopup('<b>任务: '+q.name+'</b><br/>等级: '+q.level+'<br/>区域: '+q.zone)
     .addTo(questsLayer);
  });
  data.loot.forEach(l=>{
    var lat = (l.coords[1] / 9984) * 180 - 90;
    var lng = (l.coords[0] / 13312) * 360 - 180;
    L.marker([lat, lng])
     .bindPopup('<b>掉落: '+l.name+'</b><br/>来源: '+l.source+'<br/>区域: '+l.zone)
     .addTo(lootLayer);
  });
  data.pets.forEach(p=>{
    var lat = (p.coords[1] / 9984) * 180 - 90;
    var lng = (p.coords[0] / 13312) * 360 - 180;
    L.circle([lat, lng], {radius:50000, color:'blue'})
     .bindPopup('<b>宠物: '+p.name+'</b><br/>获取: '+p.obtain+'<br/>区域: '+p.zone)
     .addTo(petsLayer);
  });
});

document.getElementById('toggleQuests').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(questsLayer); else map.removeLayer(questsLayer);
});
document.getElementById('toggleLoot').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(lootLayer); else map.removeLayer(lootLayer);
});
document.getElementById('togglePets').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(petsLayer); else map.removeLayer(petsLayer);
});
</script>
</body>
</html>
