<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8"/>
<title>Wo<PERSON> 真实地图切片 Demo</title>
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
<style>
  html, body, #map { height:100%; margin:0; padding:0; }
  .controls {
    position:absolute; top:10px; left:10px;
    z-index:1000; background:#fffbea; padding:8px;
    border-radius:6px; border:1px solid #ccc;
  }
</style>
</head>
<body>
<div class="controls">
  <strong>图层</strong><br/>
  <label><input type="checkbox" id="toggleQuests" checked/> 任务</label><br/>
  <label><input type="checkbox" id="toggleLoot" checked/> 掉落</label><br/>
  <label><input type="checkbox" id="togglePets" checked/> 宠物</label>
</div>
<div id="map"></div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
var imgWidth = 13312, imgHeight = 9984;

var map = L.map('map', {
  crs: L.CRS.Simple,
  minZoom: 0,
  maxZoom: 6
});

var bounds = [[0,0],[imgHeight,imgWidth]];
map.fitBounds(bounds);
L.tileLayer('tiles/{z}/{x}/{y}.png', {
  tileSize: 256,
  noWrap: true,
  bounds: bounds
}).addTo(map);

// ---- 以下是加载任务/掉落/宠物数据 ----
var questsLayer = L.layerGroup().addTo(map);
var lootLayer   = L.layerGroup().addTo(map);
var petsLayer   = L.layerGroup().addTo(map);

fetch('map_data_sample.json').then(r=>r.json()).then(data=>{
  data.quests.forEach(q=>{
    L.circleMarker([q.coords[1], q.coords[0]], {radius:6, color:'red'})
     .bindPopup('<b>任务: '+q.name+'</b><br/>等级: '+q.level+'<br/>区域: '+q.zone)
     .addTo(questsLayer);
  });
  data.loot.forEach(l=>{
    L.marker([l.coords[1], l.coords[0]])
     .bindPopup('<b>掉落: '+l.name+'</b><br/>来源: '+l.source+'<br/>区域: '+l.zone)
     .addTo(lootLayer);
  });
  data.pets.forEach(p=>{
    L.circle([p.coords[1], p.coords[0]], {radius:10, color:'blue'})
     .bindPopup('<b>宠物: '+p.name+'</b><br/>获取: '+p.obtain+'<br/>区域: '+p.zone)
     .addTo(petsLayer);
  });
});

document.getElementById('toggleQuests').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(questsLayer); else map.removeLayer(questsLayer);
});
document.getElementById('toggleLoot').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(lootLayer); else map.removeLayer(lootLayer);
});
document.getElementById('togglePets').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(petsLayer); else map.removeLayer(petsLayer);
});
</script>
</body>
</html>
