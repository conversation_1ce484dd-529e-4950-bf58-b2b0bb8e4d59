<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8"/>
<title>WoW 地图 - 最终版本</title>
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
<style>
  html, body, #map { height:100%; margin:0; padding:0; }
  .controls {
    position:absolute; top:10px; left:10px;
    z-index:1000; background:#fffbea; padding:8px;
    border-radius:6px; border:1px solid #ccc;
  }
</style>
</head>
<body>
<div class="controls">
  <strong>图层</strong><br/>
  <label><input type="checkbox" id="toggleQuests" checked/> 任务</label><br/>
  <label><input type="checkbox" id="toggleLoot" checked/> 掉落</label><br/>
  <label><input type="checkbox" id="togglePets" checked/> 宠物</label>
</div>
<div id="map"></div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
// Create map with proper tile layer that works with your structure
var map = L.map('map', {
  crs: L.CRS.Simple,
  minZoom: 0,
  maxZoom: 5
});

// Define the actual image size based on your tile structure
// At max zoom (5): 26x24 tiles, each 256px = 6656x6144 pixels
var imgWidth = 6656;
var imgHeight = 6144;

// Set bounds for the full image
var bounds = [[0, 0], [imgHeight, imgWidth]];
map.fitBounds(bounds);

// Create a custom tile layer that handles your tile structure correctly
var tileLayer = L.tileLayer('tiles/{z}/{x}/{y}.png', {
  tileSize: 256,
  noWrap: true,
  bounds: bounds,
  maxNativeZoom: 5,
  // Custom tile loading function that only loads existing tiles
  createTile: function(coords, done) {
    var tile = document.createElement('img');
    
    // Define the actual tile ranges for each zoom level based on your files
    var maxTiles = {
      0: {maxX: 0, maxY: 0},    // 1x1 tile
      1: {maxX: 1, maxY: 1},    // 2x2 tiles  
      2: {maxX: 3, maxY: 2},    // 4x3 tiles
      3: {maxX: 7, maxY: 5},    // 8x6 tiles
      4: {maxX: 15, maxY: 11},  // 16x12 tiles
      5: {maxX: 25, maxY: 23}   // 26x24 tiles
    };
    
    var z = coords.z;
    var x = coords.x;
    var y = coords.y;
    
    // Check if coordinates are within valid range
    if (z in maxTiles && x >= 0 && y >= 0 && x <= maxTiles[z].maxX && y <= maxTiles[z].maxY) {
      tile.src = 'tiles/' + z + '/' + x + '/' + y + '.png';
    } else {
      // Create transparent tile for invalid coordinates
      var canvas = document.createElement('canvas');
      canvas.width = 256;
      canvas.height = 256;
      tile.src = canvas.toDataURL();
    }
    
    tile.onload = function() { done(null, tile); };
    tile.onerror = function() { 
      // If tile fails to load, create transparent tile
      var canvas = document.createElement('canvas');
      canvas.width = 256;
      canvas.height = 256;
      tile.src = canvas.toDataURL();
      done(null, tile);
    };
    
    return tile;
  }
}).addTo(map);

// Create layer groups for different marker types
var questsLayer = L.layerGroup().addTo(map);
var lootLayer   = L.layerGroup().addTo(map);
var petsLayer   = L.layerGroup().addTo(map);

// Load and display markers with correct coordinate transformation
fetch('map_data_sample.json').then(r=>r.json()).then(data=>{
  data.quests.forEach(q=>{
    // Convert coordinates: your data seems to be in a different coordinate system
    // Let's assume the coordinates are relative to the full image size
    var x = (q.coords[0] / 13312) * imgWidth;   // Scale X to actual image width
    var y = (q.coords[1] / 9984) * imgHeight;   // Scale Y to actual image height
    
    var marker = L.circleMarker([y, x], {radius:8, color:'red', fillColor:'red', fillOpacity:0.7, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#d2691e;">🎯 任务: ${q.name}</h3>
         <p><strong>等级:</strong> ${q.level}</p>
         <p><strong>区域:</strong> ${q.zone}</p>
         <p><strong>任务给予者:</strong> ${q.giver.name}</p>
         <p><strong>备注:</strong> ${q.notes}</p>
         <p><a href="${q.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>原始坐标: (${q.coords[0]}, ${q.coords[1]})</small></p>
         <p><small>地图坐标: (${Math.round(x)}, ${Math.round(y)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 2, map.getMaxZoom()));
     })
     .addTo(questsLayer);
  });
  
  data.loot.forEach(l=>{
    var x = (l.coords[0] / 13312) * imgWidth;
    var y = (l.coords[1] / 9984) * imgHeight;
    
    var marker = L.marker([y, x])
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#8b4513;">💰 掉落: ${l.name}</h3>
         <p><strong>来源:</strong> ${l.source}</p>
         <p><strong>区域:</strong> ${l.zone}</p>
         <p><strong>掉落率:</strong> ${l.drop_rate}</p>
         <p><strong>物品ID:</strong> ${l.item_id}</p>
         <p><a href="${l.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>原始坐标: (${l.coords[0]}, ${l.coords[1]})</small></p>
         <p><small>地图坐标: (${Math.round(x)}, ${Math.round(y)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 2, map.getMaxZoom()));
     })
     .addTo(lootLayer);
  });
  
  data.pets.forEach(p=>{
    var x = (p.coords[0] / 13312) * imgWidth;
    var y = (p.coords[1] / 9984) * imgHeight;
    
    var marker = L.circle([y, x], {radius:200, color:'blue', fillColor:'blue', fillOpacity:0.5, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#4169e1;">🐾 宠物: ${p.name}</h3>
         <p><strong>获取方式:</strong> ${p.obtain}</p>
         <p><strong>区域:</strong> ${p.zone}</p>
         <p><strong>备注:</strong> ${p.notes}</p>
         <p><strong>宠物ID:</strong> ${p.id}</p>
         <p><a href="${p.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>原始坐标: (${p.coords[0]}, ${p.coords[1]})</small></p>
         <p><small>地图坐标: (${Math.round(x)}, ${Math.round(y)})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 2, map.getMaxZoom()));
     })
     .addTo(petsLayer);
  });
});

// Layer toggle controls
document.getElementById('toggleQuests').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(questsLayer); else map.removeLayer(questsLayer);
});
document.getElementById('toggleLoot').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(lootLayer); else map.removeLayer(lootLayer);
});
document.getElementById('togglePets').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(petsLayer); else map.removeLayer(petsLayer);
});
</script>
</body>
</html>
