<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8"/>
<title>WoW 地图 - 简单版本</title>
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css"/>
<style>
  html, body, #map { height:100%; margin:0; padding:0; }
  .controls {
    position:absolute; top:10px; left:10px;
    z-index:1000; background:#fffbea; padding:8px;
    border-radius:6px; border:1px solid #ccc;
  }
</style>
</head>
<body>
<div class="controls">
  <strong>图层</strong><br/>
  <label><input type="checkbox" id="toggleQuests" checked/> 任务</label><br/>
  <label><input type="checkbox" id="toggleLoot" checked/> 掉落</label><br/>
  <label><input type="checkbox" id="togglePets" checked/> 宠物</label>
</div>
<div id="map"></div>

<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
// Create a simple map that just shows the base tile at zoom 0
var map = L.map('map', {
  crs: L.CRS.Simple,
  minZoom: 0,
  maxZoom: 3,
  center: [128, 128],
  zoom: 0
});

// Set bounds for a 256x256 image (the base tile)
var bounds = [[0, 0], [256, 256]];

// Add the base tile as an image overlay instead of using tile layer
L.imageOverlay('tiles/0/0/0.png', bounds).addTo(map);

// Fit the map to show the entire image
map.fitBounds(bounds);

// Create layer groups for different marker types
var questsLayer = L.layerGroup().addTo(map);
var lootLayer   = L.layerGroup().addTo(map);
var petsLayer   = L.layerGroup().addTo(map);

// Load and display markers
fetch('map_data_sample.json').then(r=>r.json()).then(data=>{
  data.quests.forEach(q=>{
    // Convert original coordinates to the 256x256 image bounds
    var x = (q.coords[0] / 13312) * 256;
    var y = (q.coords[1] / 9984) * 256;
    
    var marker = L.circleMarker([y, x], {radius:8, color:'red', fillColor:'red', fillOpacity:0.7, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#d2691e;">🎯 任务: ${q.name}</h3>
         <p><strong>等级:</strong> ${q.level}</p>
         <p><strong>区域:</strong> ${q.zone}</p>
         <p><strong>任务给予者:</strong> ${q.giver.name}</p>
         <p><strong>备注:</strong> ${q.notes}</p>
         <p><a href="${q.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>坐标: (${q.coords[0]}, ${q.coords[1]})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(questsLayer);
  });
  
  data.loot.forEach(l=>{
    var x = (l.coords[0] / 13312) * 256;
    var y = (l.coords[1] / 9984) * 256;
    
    var marker = L.marker([y, x])
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#8b4513;">💰 掉落: ${l.name}</h3>
         <p><strong>来源:</strong> ${l.source}</p>
         <p><strong>区域:</strong> ${l.zone}</p>
         <p><strong>掉落率:</strong> ${l.drop_rate}</p>
         <p><strong>物品ID:</strong> ${l.item_id}</p>
         <p><a href="${l.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>坐标: (${l.coords[0]}, ${l.coords[1]})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(lootLayer);
  });
  
  data.pets.forEach(p=>{
    var x = (p.coords[0] / 13312) * 256;
    var y = (p.coords[1] / 9984) * 256;
    
    var marker = L.circle([y, x], {radius:10, color:'blue', fillColor:'blue', fillOpacity:0.5, weight:2})
     .bindPopup(`
       <div style="min-width:200px;">
         <h3 style="margin:0 0 10px 0; color:#4169e1;">🐾 宠物: ${p.name}</h3>
         <p><strong>获取方式:</strong> ${p.obtain}</p>
         <p><strong>区域:</strong> ${p.zone}</p>
         <p><strong>备注:</strong> ${p.notes}</p>
         <p><strong>宠物ID:</strong> ${p.id}</p>
         <p><a href="${p.wowhead}" target="_blank">查看 Wowhead 详情</a></p>
         <p><small>坐标: (${p.coords[0]}, ${p.coords[1]})</small></p>
       </div>
     `)
     .on('click', function(e) {
       map.setView([y, x], Math.min(map.getZoom() + 1, map.getMaxZoom()));
     })
     .addTo(petsLayer);
  });
});

// Layer toggle controls
document.getElementById('toggleQuests').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(questsLayer); else map.removeLayer(questsLayer);
});
document.getElementById('toggleLoot').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(lootLayer); else map.removeLayer(lootLayer);
});
document.getElementById('togglePets').addEventListener('change', e=>{
  if(e.target.checked) map.addLayer(petsLayer); else map.removeLayer(petsLayer);
});
</script>
</body>
</html>
