#!/bin/bash
# Example script to download Teeb map from Internet Archive and create tiles with gdal2tiles.
# Usage: bash download_and_slice.sh
FNAME="azeroth_full.png"
# download (uncomment and edit URL if needed)
# wget -O "$FNAME" "https://archive.org/download/wow_classic_high_resolution_world_terrain_map_azeroth/wow_classic_high_resolution_world_terrain_map_azeroth.png"
echo "If you have downloaded the PNG, place it as $FNAME in this folder."
echo "Then run gdal2tiles on it, for example:"
echo "gdal2tiles.py -z 0-5 -w none -r lanczos $FNAME tiles/"
